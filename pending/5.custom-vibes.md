---
title: Custom Vibes
description: Create and manage custom emotional vibes for personalized speech generation.
---

Custom Vibes allow you to create, manage, and use personalized emotional expressions for your text-to-speech conversions. This feature enables you to define specific emotional contexts and prompts that can be reused across multiple speech generation requests, providing consistent and tailored emotional expression for your applications.

## Overview

The Custom Vibes API provides comprehensive functionality to:
- Create custom emotional vibes with specific prompts
- Retrieve and manage your personal vibe library
- Access master vibes provided by the platform
- Search and find specific vibes
- Update and delete existing custom vibes

## Get User Custom Vibes
`GET https://api.geminigen.ai/uapi/v1/custom-vibes`

Retrieve all custom vibes created by the authenticated user.

### Example Request
::code-group
```bash [terminal]
curl -X GET https://api.geminigen.ai/uapi/v1/custom-vibes \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/custom-vibes"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.get(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/custom-vibes";
const headers = {
    "x-api-key": "<your api key>"
};

axios.get(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "type": "user_vibe",
      "id": 1,
      "created_at": "2025-06-23T10:30:00",
      "vibe": "Professional Presenter",
      "prompt": "Speak with confidence and authority, as if presenting to a business audience",
      "user_id": 60788,
      "updated_at": "2025-06-23T10:30:00"
    },
    {
      "type": "user_vibe",
      "id": 2,
      "created_at": "2025-06-23T11:15:00",
      "vibe": "Storyteller",
      "prompt": "Use a warm, engaging tone with dramatic pauses and expressive inflection",
      "user_id": 60788,
      "updated_at": "2025-06-23T11:15:00"
    }
  ],
  "message": null
}
```

## Create Custom Vibe
`POST https://api.geminigen.ai/uapi/v1/custom-vibes`

Create a new custom vibe with a specific emotional context and prompt.

### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/custom-vibes \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
  }'
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/custom-vibes"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/custom-vibes";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`vibe` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name or title of the custom vibe. This should be descriptive and help you identify the emotional context.

`prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The detailed prompt that describes how the emotion should be expressed. This guides the AI in generating speech with the desired emotional characteristics.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "type": "user_vibe",
    "id": 3,
    "created_at": "2025-06-23T12:00:00",
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action",
    "user_id": 60788,
    "updated_at": "2025-06-23T12:00:00"
  },
  "message": null
}
```

## Get Master Vibes
`GET https://api.geminigen.ai/uapi/v1/master-vibes`

Retrieve all master vibes provided by the platform. These are pre-defined emotional contexts that you can use in your speech generation.

### Example Request
::code-group
```bash [terminal]
curl -X GET https://api.geminigen.ai/uapi/v1/master-vibes \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/master-vibes"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.get(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/master-vibes";
const headers = {
    "x-api-key": "<your api key>"
};

axios.get(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "type": "api_vibe",
      "id": 1,
      "created_at": "2025-06-23T09:00:00",
      "vibe": "Happy",
      "prompt": "Express joy and positivity with an upbeat tone",
      "user_id": null,
      "updated_at": "2025-06-23T09:00:00"
    },
    {
      "type": "api_vibe",
      "id": 2,
      "created_at": "2025-06-23T09:00:00",
      "vibe": "Calm",
      "prompt": "Speak in a peaceful, relaxed manner with steady pacing",
      "user_id": null,
      "updated_at": "2025-06-23T09:00:00"
    },
    {
      "type": "api_vibe",
      "id": 3,
      "created_at": "2025-06-23T09:00:00",
      "vibe": "Excited",
      "prompt": "Show enthusiasm and energy with animated expression",
      "user_id": null,
      "updated_at": "2025-06-23T09:00:00"
    }
  ],
  "message": null
}
```

## Update Custom Vibe
`PUT https://api.geminigen.ai/uapi/v1/custom-vibes/{id}`

Update an existing custom vibe by its ID.

### Example Request
::code-group
```bash [terminal]
curl -X PUT https://api.geminigen.ai/uapi/v1/custom-vibes/3 \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
  }'
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/custom-vibes/3"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
}

response = requests.put(url, headers=headers, json=data)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/custom-vibes/3";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
};

axios.put(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`vibe` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The updated name or title of the custom vibe.

`prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The updated prompt that describes how the emotion should be expressed.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "type": "user_vibe",
    "id": 3,
    "created_at": "2025-06-23T12:00:00",
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action",
    "user_id": 60788,
    "updated_at": "2025-06-23T12:30:00"
  },
  "message": null
}
```

## Delete Custom Vibe
`DELETE https://api.geminigen.ai/uapi/v1/custom-vibes/{id}`

Delete a custom vibe by its ID.

### Example Request
::code-group
```bash [terminal]
curl -X DELETE https://api.geminigen.ai/uapi/v1/custom-vibes/3 \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/custom-vibes/3"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.delete(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/custom-vibes/3";
const headers = {
    "x-api-key": "<your api key>"
};

axios.delete(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Path Parameters

`id` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier of the custom vibe to delete.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "type": "user_vibe",
    "id": 3,
    "created_at": "2025-06-23T12:00:00",
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action",
    "user_id": 60788,
    "updated_at": "2025-06-23T12:30:00",
    "user": {},
    "tts_histories": [],
    "story_blocks": []
  },
  "message": null
}
```

## Search Custom Vibes
`GET https://api.geminigen.ai/uapi/v1/custom-vibes/search`

Search for custom vibes by keyword.

### Example Request
::code-group
```bash [terminal]
curl -X GET "https://api.geminigen.ai/uapi/v1/custom-vibes/search?key=motivational" \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/custom-vibes/search"
headers = {
    "x-api-key": "<your api key>"
}
params = {
    "key": "motivational"
}

response = requests.get(url, headers=headers, params=params)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/custom-vibes/search";
const headers = {
    "x-api-key": "<your api key>"
};
const params = {
    key: "motivational"
};

axios.get(url, { headers, params })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Query Parameters

`key` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The search keyword to find matching vibes. The search will look for matches in both vibe names and prompts.

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "type": "user_vibe",
      "id": 1,
      "created_at": "2025-06-23T12:00:00",
      "vibe": "Motivational Coach",
      "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action",
      "user_id": 60788,
      "updated_at": "2025-06-23T12:00:00"
    }
  ],
  "message": null
}
```

## Response Attributes

All API responses follow a consistent structure with the following common attributes:

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object|array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The main response data. Can be an object for single items or an array for multiple items.

`message` [string|null]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Additional message about the operation. Can be null for successful operations.

### Vibe Object Attributes

Each vibe object in the response contains the following attributes:

`type` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The type of vibe. Possible values:
- `user_vibe`: Custom vibes created by the user
- `api_vibe`: Master vibes provided by the platform

`id` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the vibe.

`created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the vibe was created (ISO 8601 format).

`vibe` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name or title of the vibe.

`prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The detailed prompt that describes how the emotion should be expressed.

`user_id` [integer|null]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The ID of the user who created the vibe. Null for master vibes (api_vibe type).

`updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the vibe was last updated (ISO 8601 format).

### Additional Attributes (Delete Response Only)

When deleting a vibe, the response includes additional relationship data:

`user` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

User information associated with the vibe (typically empty object).

`tts_histories` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Array of TTS history records that used this vibe (typically empty when deleting).

`story_blocks` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Array of story blocks that used this vibe (typically empty when deleting).

## Using Custom Vibes in Speech Generation

Once you have created custom vibes, you can use them in your emotion-to-speech requests by referencing the vibe ID:

```json
{
  "model": "audio_stable",
  "voice_id": "OA001",
  "speed": 1,
  "input": "Welcome to our presentation today!",
  "vibe_id": 1,
  "emotion": "professional",
  "custom_prompt": "Speak with confidence and authority, as if presenting to a business audience"
}
```

## Best Practices

### Creating Effective Vibes
- **Be Specific**: Use detailed prompts that clearly describe the desired emotional expression
- **Use Context**: Include situational context in your prompts (e.g., "as if speaking to children", "like a news anchor")
- **Test and Iterate**: Create test audio with your vibes and refine the prompts based on results

### Organizing Your Vibes
- **Descriptive Names**: Use clear, descriptive names that make it easy to identify the right vibe
- **Categorize**: Consider creating vibes for different use cases (presentations, storytelling, customer service, etc.)
- **Regular Cleanup**: Remove unused vibes to keep your library organized

### Prompt Writing Tips
- **Tone Descriptors**: Use words like "warm", "authoritative", "gentle", "energetic"
- **Pacing Instructions**: Include guidance on speed and rhythm ("with dramatic pauses", "at a steady pace")
- **Emotional Context**: Specify the underlying emotion and intensity level
- **Audience Consideration**: Mention the intended audience when relevant
