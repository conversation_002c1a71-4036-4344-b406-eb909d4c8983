---
title: Story Maker
description: Generate lifelike speech from multiple text inputs and create engaging audio stories.
---

The GeminiGen.AI Story Maker API allows you to convert multiple text inputs into high-quality, natural-sounding speech. You can use this API to create engaging audio stories, generate voiceovers for multimedia content, or narrate interactive experiences.

## Story Maker
`POST https://api.geminigen.ai/uapi/v1/story-maker`

This endpoint allows you to convert multiple text inputs into speech. You can customize the voice, speed, and model used for the conversion.


### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/story-maker \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
        "name": "Name of the story",
        "blocks": [
          {
            "name": "Name of the block",
            "input": "Text to be converted into speech",
            "silence_before": 2,
            "voice_id": "OA001",
            "emotion": "neutral",
            "model": "tts-1",
            "speed": 1,
            "duration": 0
          }
        ]
      }'
```

```ts [py]
import requests

url = "https://api.geminigen.ai/uapi/v1/story-maker"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
  "name": "Name of the story",
  "blocks": [
    {
      "name": "Name of the block",
      "input": "Text to be converted into speech",
      "silence_before": 2,
      "voice_id": "OA001",
      "emotion": "neutral",
      "model": "tts-1",
      "speed": 1,
      "duration": 0
    }
  ]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/story-maker";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
  "name": "Name of the story",
  "blocks": [
    {
      "name": "Name of the block",
      "input": "Text to be converted into speech",
      "silence_before": 2,
      "voice_id": "OA001",
      "emotion": "neutral",
      "model": "tts-1",
      "speed": 1,
      "duration": 0
    }
  ]
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the story.

`blocks` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

An array of text blocks to be converted into speech.

`blocks.name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the block.

`blocks.input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text to be converted into speech.

`blocks.silence_before` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The duration of silence before the speech starts, in seconds.

`blocks.voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion.

`blocks.emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The emotion of the speech. (Coming soon)

`blocks.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion.

`blocks.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`blocks.duration` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The duration of the speech, in seconds.


### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "uuid": "cfc26b24-ae61-11ef-9913-9e4d64684f7d",
    "voice_id": "",
    "speed": 1,
    "model": "tts-1",
    "tts_input": "Name of the story",
    "estimated_credit": 0,
    "used_credit": 0,
    "status": 1,
    "status_percentage": 1,
    "error_message": "",
    "speaker_name": null,
    "created_at": "2024-11-29T14:54:19",
    "updated_at": "2024-11-29T14:54:19",
    "file_size": 0
  }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result of the story-maker conversion.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the conversion.

`result.voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion.

`result.tts_input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text that was converted into speech.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits used for the conversion.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the conversion.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The status of the conversion. Possible values are:

<!-- CONVERTING = 1
JOINING_AUDIO = 12
MERGING_AUDIO = 13
DOWNLOADING_AUDIO = 14
REWORKING = 11
COMPLETED = 2
ERROR = 3 -->

- `1`: Converting
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of the conversion that has been completed.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The error message, if any.

`result.speaker_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the speaker.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was last updated.

`result.file_size` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The size of the audio file, in bytes.
