export default defineAppConfig({
  ui: {
    colors: {
      primary: 'orange',
      neutral: 'neutral'
    }
  },
  uiPro: {
    footer: {
      slots: {
        root: 'border-t border-default',
        left: 'text-sm text-muted'
      }
    }
  },
  seo: {
    siteName: 'GeminiGen AI - Advanced AI Generation Services. Documentation'
  },
  header: {
    title: '',
    to: '/',
    logo: {
      alt: '',
      light: '',
      dark: ''
    },
    search: true,
    colorMode: true,
    links: []
  },
  footer: {
    credits: `Copyright © ${new Date().getFullYear()}, GeminiGen.AI`,
    colorMode: false,
    links: [{
      'label': 'Privacy',
      'to': 'https://geminigen.ai/privacy',
      'target': '_blank',
      'aria-label': 'Privacy Policy'
    },
    {
      'label': 'Terms',
      'to': 'https://geminigen.ai/terms',
      'target': '_blank',
      'aria-label': 'Terms of Service'
    },
    {
      'icon': 'mingcute:voice-line',
      'to': 'https://ttsopenai.com/',
      'target': '_blank',
      'aria-label': 'Text To Speech OpenAI'
    },
    {
      'icon': 'hugeicons:translate',
      'to': 'https://doctransgpt.com/',
      'target': '_blank',
      'aria-label': 'DoctransGPT'
    },
    {
      icon: 'i-simple-icons-discord',
      to: 'https://discord.gg/vJnYe86T8F',
      target: '_blank'
    }]
  }

})
